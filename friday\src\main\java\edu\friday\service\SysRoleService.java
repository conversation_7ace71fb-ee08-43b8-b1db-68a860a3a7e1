package edu.friday.service;

import edu.friday.common.result.TableDataInfo;
import edu.friday.model.SysRole;
import edu.friday.model.vo.SysRoleVO;
import org.springframework.data.domain.Pageable;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;

/**
 * 角色 业务层接口
 */
public interface SysRoleService {
    TableDataInfo selectRoleList(SysRoleVO role, Pageable page);
    List<SysRole> selectRoleAll();
    List<Long> selectRoleListByUserId(Long userId);
    @Transactional
    int insertRole(SysRoleVO role);
    String checkRoleNameUnique(SysRoleVO role);

    String checkRoleKeyUnique(SysRoleVO role);
    @Transactional
    int deleteRoleByIds(Long[] roleIds);
    @Transactional

    void checkRoleAllowed(SysRoleVO role);
    SysRole selectRoleById(Long roleId);
    int countUserRoleByRoleId(Long roleId);
    @Transactional
    int updateRole(SysRoleVO role);
    @Transactional
    int updateRoleStatus(SysRoleVO role);

    Set<String> selectRolePermissionByUserId(Long userId);
}
