(1)根据本学期所学的JavaEE框架技术知识和技能，独立完成JavaEE 三层架构的程序设计与开发:
(2)是一个完整的、可运行的基于Springboot 的Javaee 后端应用程序:

2、项目要求
(1)内容:基于Springboot框架的后端接口开发
(2)结构:正确使用控制层，服务层，数据访问层结构，正确使用数据库映射模型和视图对象模型:
(3)面向对象开发技术:能正确使用常用Java类，接口定义:
(4)数据库工具类:使用 Spring JPA,Mybatis,Mybatis-plus,JdbcTemplate 中的任意种访问数据店
(5)在教材源码的基础上进行应用扩展，在新的应用中设计数据库表并根据应用逻辑对数据库进行增删查改操作。具体应用可以参考以下内容,也可以自行设计。(a)图书管理功能，在原项目中添加图书管理功能。可以创建一个Book表，实现对图书进行模糊查询和分页查询功能，针对图书id查询指定图书详细信息功能添加图书功能，修改图书功能，删除一条或若干条图书功能。(添加和修改图书时图书名称不能相同)
(b)课程管理功能，在原项目中添加课程管理功能。可以考虑创建一个Course表实现对课程的模糊查询和分页查询功能，针对课程Id查询指定课程详细信息功能，添加课程，修改课程功能，删除一条或若干条课程功能。(添加和需修改课程时，课程名称不能相同)(c)商品库存功能。在原新项目上添加商品管理功能。可以考虑创建一个Goods表
实现对库存商品类型的模糊查询和分页查询功能，添加商品类型功能，商品入库/出库功能，删除一种或若干种商品功能(添加商品种类时商品名称不能重复)
3、项目程序代码编写要求(1)程序代码要保证正确无误，能够在Idea开发工具中直接运行查看效果:(2)程序代码可读性强，关键方法要有输入输出参数说明以及方法功能说明:(3)程序代码命名规范合理，代码缩进格式合理。
4、学生提交材料的相关要求(1)能够完整运行的springboot代码放在一个文件夹里面，Friday.sql文件保存在resource日录中，打包压缩成zip或rar文件:(2)杜绝任何抄袭现象，源码相同内容的作品一律视零分处理:(3)上交作品需压缩后才能提交，文件命名规则为学号+姓名: