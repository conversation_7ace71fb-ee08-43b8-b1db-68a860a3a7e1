package edu.friday.repository;
import edu.friday.repository.custom.SysUserCustomRepository;
import org.springframework.data.jpa.repository.JpaRepository;
import edu.friday.model.SysUser;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * 用户表 数据层
 */
@Repository
public interface SysUserRepository extends JpaRepository<SysUser, Long> , SysUserCustomRepository {
    @Modifying
    @Query(value = " update sys_user set del_flag = '2' where user_id in :userIds ", nativeQuery = true)
    int deleteUserByIds(@Param("userIds") Long[] userIds);
    @Modifying
    @Query(value = " delete from sys_user_role where user_id=:userId ", nativeQuery = true)
    int deleteUserRoleByUserId(@Param("userId")Long userId);
}