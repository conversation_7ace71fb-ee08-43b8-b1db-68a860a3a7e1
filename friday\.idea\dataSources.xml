<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="DataSourceManagerImpl" format="xml" multifile-model="true">
    <data-source source="LOCAL" name="friday" uuid="85de7839-4321-4659-a1c5-1eab1944c478">
      <driver-ref>mysql.8</driver-ref>
      <synchronize>true</synchronize>
      <jdbc-driver>com.mysql.cj.jdbc.Driver</jdbc-driver>
      <jdbc-url>***************************************************************************************************************************************************************</jdbc-url>
    </data-source>
    <data-source source="LOCAL" name="friday/Hibernate" uuid="d23d50ab-2910-4930-9fa8-37fb0832cd96">
      <driver-ref>azure.ms</driver-ref>
      <synchronize>true</synchronize>
      <imported>true</imported>
      <remarks>Hibernate</remarks>
      <jdbc-driver>com.microsoft.sqlserver.jdbc.SQLServerDriver</jdbc-driver>
      <jdbc-url>******************************************************</jdbc-url>
    </data-source>
  </component>
</project>