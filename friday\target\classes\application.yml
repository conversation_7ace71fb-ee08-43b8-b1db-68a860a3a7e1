server:
  port: 8080

spring:

  devtools:
    restart:
      #需要实时更新的目录
      additional-paths: resources/**,static/**,templates/**

  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************************************************
    username: root
    password: 123456
    platform: mysql
    type: com.alibaba.druid.pool.DruidDataSource
    initialSize: 1
    minIdle: 3
    maxActive: 20
    maxWait: 60000
    timeBetweenEvictionRunsMillis: 60000
    minEvictableIdleTimeMillis: 30000
    validationQuery: select 'x'
    testWhileIdle: true
    testOnBorrow: false
    testOnReturn: false
    poolPreparedStatements: true
    maxPoolPreparedStatementPerConnectionSize: 20
    # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
    filters: stat,wall,slf4j
    # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
    connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
  jpa:
    show-sql: true
    properties:
      hibernate.format_sql: true
  # redis 配置
  redis:
    # 地址
    host: localhost
    # 端口，默认为6379
    port: 6379
    # 密码
    password:
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms

#mybatis:
#  type-aliases-package: edu.friday.domain
#  mapper-locations: classpath:/mybatis-mappers/*
#  configuration:
#    mapUnderscoreToCamelCase: true

swagger:
  base-package: edu.friday
  enabled: true


# 项目相关配置
friday:
  # 名称
  name: friday
  # 版本
  version: 1.0.0
  # 版权年份
  copyrightYear: 2020
  # 实例演示开关
  demoEnabled: true
  # 文件路径 示例（ Windows配置c:/friday/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: c:/friday/uploadPath
  # 文件服务域名 fileHost： http://localhost:8080/
  fileHost: http://localhost:8080
  # 获取ip地址开关
  addressEnabled: true
#baseInfo
#api-url: /api

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌秘钥
  #  secret: abcdefghijklmnopqrstuvwxyz
  secret: (FRIDAY:)_$^11244^%$_(IS:)_@@++--(COOL:)_++++_.sds_(GUY:)
  # 令牌有效期（默认30分钟）
  expireTime: 60


# 代码生成
gen:
  # 作者
  author: friday
  # 默认生成包路径 system 需改成自己的模块名称 如 system monitor tool
  packageName: edu.friday.system
  # 自动去除表前缀，默认是true
  autoRemovePre: false
  # 表前缀（生成类名不会包含表前缀，多个用逗号分隔）
  tablePrefix: sys_